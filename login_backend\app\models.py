from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String
from .database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=True)
    google_id = Column(String, unique=True, index=True, nullable=True)
    is_active = Column(Boolean, default=True)

    # 這裡可以根據你的需求加上 `birth_date`, `gender` 等欄位

    # 但為了聚焦在核心功能，我們先保持簡單